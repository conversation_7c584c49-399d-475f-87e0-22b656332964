# Onboarding Module API Documentation

## Overview

The Onboarding Module provides a complete user onboarding flow for creating tenants and websites in the multi-tenant blog platform. This module handles the step-by-step process of setting up a new organization (tenant) and their first website.

## Base URL

```
http://localhost:9033/api/admin/v1/onboarding
```

## Authentication

All endpoints require JWT authentication via Bearer token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

## Multi-Tenancy Headers

For endpoints that require tenant context, include these headers:

```
X-Tenant-ID: <tenant_id>
X-Website-ID: <website_id>  // Optional for some endpoints
```

## Onboarding Flow

The recommended onboarding flow for UI implementation:

### 1. User Registration/Login
First, ensure the user is authenticated and has a valid JWT token.

### 2. Generate Tenant Code (Optional)
Help users create a unique tenant code based on their company name:

```http
GET /generate-tenant-code?tenant_name=My Company
```

### 3. Create Tenant
Create the organization/tenant:

```http
POST /tenant
```

### 4. Create Website
Create the first website for the tenant:

```http
POST /website
```

### 5. Initialize Onboarding Progress
Set up the onboarding progress tracking:

```http
POST /initialize
```

### 6. Track Progress
Monitor and update onboarding progress:

```http
GET /status
POST /steps/complete
```

## API Endpoints

### Health Check
- **GET** `/health`
- **Description**: Check if the onboarding module is running
- **Auth**: None required
- **Response**: Module status information

### Generate Tenant Code
- **GET** `/generate-tenant-code`
- **Description**: Generate a unique tenant code from a tenant name
- **Auth**: Required
- **Query Parameters**:
  - `tenant_name` (string, required): The name to generate code from
- **Response**: Generated tenant code

### Create Tenant
- **POST** `/tenant`
- **Description**: Create a new tenant (organization)
- **Auth**: Required
- **Body**: CreateTenantOnboardingRequest
- **Response**: Created tenant information

### Create Website
- **POST** `/website`
- **Description**: Create a new website for a tenant
- **Auth**: Required
- **Headers**: `X-Tenant-ID` required
- **Body**: CreateWebsiteOnboardingRequest
- **Response**: Created website information

### Initialize Onboarding
- **POST** `/initialize`
- **Description**: Initialize onboarding progress tracking
- **Auth**: Required
- **Headers**: `X-Tenant-ID` and `X-Website-ID` required
- **Response**: Initial onboarding status

### Get Onboarding Status
- **GET** `/status`
- **Description**: Get current onboarding progress
- **Auth**: Required
- **Headers**: `X-Tenant-ID` and `X-Website-ID` required
- **Response**: Detailed onboarding progress

### Complete Onboarding Step
- **POST** `/steps/complete`
- **Description**: Mark an onboarding step as completed
- **Auth**: Required
- **Headers**: `X-Tenant-ID` and `X-Website-ID` required
- **Body**: CompleteOnboardingStepRequest
- **Response**: Next step information

### Skip Onboarding Step
- **POST** `/steps/skip`
- **Description**: Skip an onboarding step
- **Auth**: Required
- **Headers**: `X-Tenant-ID` and `X-Website-ID` required
- **Body**: SkipOnboardingStepRequest
- **Response**: Next step information

### Get Next Step
- **GET** `/next-step`
- **Description**: Get the next onboarding step
- **Auth**: Required
- **Headers**: `X-Tenant-ID` and `X-Website-ID` required
- **Response**: Next step information

### Get Website Templates
- **GET** `/templates`
- **Description**: Get available website templates
- **Auth**: Required
- **Response**: List of website templates

### Get Tenant Name Suggestions
- **GET** `/suggestions/tenant-name`
- **Description**: Get tenant name suggestions based on full name
- **Auth**: Required
- **Query Parameters**:
  - `full_name` (string, required): Full name to generate suggestions from
- **Response**: List of suggested tenant names

## Data Models

### CreateTenantOnboardingRequest
```json
{
  "tenant_type": "company|individual",
  "tenant_name": "string (required, 2-255 chars)",
  "company_name": "string (optional, 2-255 chars)",
  "tax_code": "string (optional, 5-50 chars)",
  "legal_representative": "string (optional, 2-255 chars)",
  "company_address": "string (optional)",
  "company_phone": "string (optional)",
  "company_email": "string (optional, valid email)"
}
```

### CreateWebsiteOnboardingRequest
```json
{
  "name": "string (required, 2-100 chars)",
  "subdomain": "string (optional, 3-50 chars, alphanumeric)",
  "custom_domain": "string (optional, valid FQDN)",
  "description": "string (optional)",
  "theme_id": "number (optional)"
}
```

### CompleteOnboardingStepRequest
```json
{
  "step_name": "email_verification|tenant_setup|website_setup|profile_setup|tutorial",
  "data": "object (optional, step-specific data)"
}
```

## Onboarding Steps

The onboarding process consists of 5 main steps:

1. **email_verification**: Verify user's email address
2. **tenant_setup**: Create and configure tenant
3. **website_setup**: Create and configure first website
4. **profile_setup**: Complete user profile information
5. **tutorial**: Complete onboarding tutorial

## Error Handling

All endpoints return standardized error responses:

```json
{
  "status": {
    "code": 400,
    "message": "Error description",
    "success": false,
    "error_code": "ERROR_CODE",
    "path": "/api/admin/v1/onboarding/endpoint"
  }
}
```

Common error codes:
- `INVALID_REQUEST`: Invalid request body or parameters
- `TENANT_ID_REQUIRED`: Missing X-Tenant-ID header
- `USER_NOT_AUTHENTICATED`: Missing or invalid JWT token
- `CREATE_TENANT_ERROR`: Failed to create tenant
- `CREATE_WEBSITE_ERROR`: Failed to create website

## Testing with Bruno

Use the provided Bruno collection in this directory to test all endpoints. The collection includes:

1. Authentication setup
2. Complete onboarding flow examples
3. Error case testing
4. Response validation

## UI Implementation Guidelines

### 1. Progressive Disclosure
Show onboarding steps progressively, don't overwhelm users with all steps at once.

### 2. Progress Indication
Use the completion rate and step status to show progress visually.

### 3. Error Handling
Implement proper error handling for all API calls with user-friendly messages.

### 4. Validation
Validate form inputs on the client side before sending to API.

### 5. State Management
Track onboarding state in your application to allow users to resume where they left off.

### 6. Responsive Design
Ensure the onboarding flow works well on all device sizes.

## Example Implementation Flow

```javascript
// 1. Generate tenant code
const tenantCodeResponse = await fetch('/api/admin/v1/onboarding/generate-tenant-code?tenant_name=' + encodeURIComponent(companyName));
const { tenant_code } = await tenantCodeResponse.json();

// 2. Create tenant
const tenantResponse = await fetch('/api/admin/v1/onboarding/tenant', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    tenant_type: 'company',
    tenant_name: companyName,
    // ... other fields
  })
});
const tenant = await tenantResponse.json();

// 3. Create website
const websiteResponse = await fetch('/api/admin/v1/onboarding/website', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'X-Tenant-ID': tenant.data.tenant_id,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: websiteName,
    subdomain: subdomain,
    // ... other fields
  })
});
const website = await websiteResponse.json();

// 4. Initialize onboarding
const initResponse = await fetch('/api/admin/v1/onboarding/initialize', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'X-Tenant-ID': tenant.data.tenant_id,
    'X-Website-ID': website.data.website_id
  }
});
const onboardingStatus = await initResponse.json();
```

## Support

For questions or issues with the Onboarding Module API, please refer to the Bruno collection for working examples or contact the development team.
