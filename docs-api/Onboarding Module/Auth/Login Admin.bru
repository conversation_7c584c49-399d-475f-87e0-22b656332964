meta {
  name: Login Admin
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/admin/v1/auth/login
  body: json
  auth: none
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "12345678"
  }
}

script:post-response {
  if (res.status === 200 && res.body.data && res.body.data.access_token) {
    bru.setEnvVar("admin_token", res.body.data.access_token);
  }
}

tests {
  test("should return 200", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should have access token", function() {
    expect(res.body.data.access_token).to.be.a('string');
  });
}
