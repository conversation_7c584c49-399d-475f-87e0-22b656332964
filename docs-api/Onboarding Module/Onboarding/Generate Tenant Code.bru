meta {
  name: Generate Tenant Code
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/admin/v1/onboarding/generate-tenant-code?tenant_name=My Company
  body: none
  auth: bearer
}

auth:bearer {
  token: {{admin_token}}
}

params:query {
  tenant_name: My Company
}

tests {
  test("should return 200", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should have tenant code", function() {
    expect(res.body.data.tenant_code).to.be.a('string');
  });
  
  test("tenant code should be valid format", function() {
    const tenantCode = res.body.data.tenant_code;
    expect(tenantCode).to.match(/^[a-z0-9-]+$/);
    expect(tenantCode.length).to.be.at.least(3);
    expect(tenantCode.length).to.be.at.most(50);
  });
}
