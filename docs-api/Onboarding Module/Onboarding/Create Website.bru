meta {
  name: Create Website
  type: http
  seq: 4
}

post {
  url: {{api_url}}/api/admin/v1/onboarding/website
  body: json
  auth: bearer
}

auth:bearer {
  token: {{admin_token}}
}

headers {
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "name": "My Company Website",
    "subdomain": "mycompany",
    "description": "Official website for My Company"
  }
}

script:post-response {
  if (res.status === 200 && res.body.data && res.body.data.website_id) {
    bru.setEnvVar("website_id", res.body.data.website_id);
  }
}

tests {
  test("should return 200", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should have website data", function() {
    expect(res.body.data.website_id).to.be.a('number');
    expect(res.body.data.name).to.equal('My Company Website');
    expect(res.body.data.subdomain).to.equal('mycompany');
  });
  
  test("should have success message", function() {
    expect(res.body.data.message).to.equal('Website created successfully');
  });
}
