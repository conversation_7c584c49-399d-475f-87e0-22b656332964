meta {
  name: Get Onboarding Status
  type: http
  seq: 6
}

get {
  url: {{api_url}}/api/admin/v1/onboarding/status
  body: none
  auth: bearer
}

auth:bearer {
  token: {{admin_token}}
}

headers {
  X-Tenant-ID: {{tenant_id}}
  X-Website-ID: {{website_id}}
}

tests {
  test("should return 200", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should have onboarding status", function() {
    expect(res.body.data.onboarding_status).to.be.oneOf(['not_started', 'in_progress', 'completed']);
    expect(res.body.data.progress).to.be.an('array');
    expect(res.body.data.total_steps).to.equal(5);
    expect(res.body.data.completed_steps).to.be.at.least(0);
    expect(res.body.data.completion_rate).to.be.at.least(0);
    expect(res.body.data.completion_rate).to.be.at.most(100);
  });
  
  test("should have valid progress steps", function() {
    const validSteps = ['email_verification', 'tenant_setup', 'website_setup', 'profile_setup', 'tutorial'];
    res.body.data.progress.forEach(step => {
      expect(validSteps).to.include(step.step_name);
      expect(['not_started', 'in_progress', 'completed', 'skipped']).to.include(step.status);
    });
  });
}
