meta {
  name: Create Tenant
  type: http
  seq: 3
}

post {
  url: {{api_url}}/api/admin/v1/onboarding/tenant
  body: json
  auth: bearer
}

auth:bearer {
  token: {{admin_token}}
}

body:json {
  {
    "tenant_type": "company",
    "tenant_name": "My Test Company",
    "company_name": "My Test Company Ltd",
    "tax_code": "*********",
    "legal_representative": "<PERSON>",
    "company_address": "123 Main St, City, Country",
    "company_phone": "+*********0",
    "company_email": "<EMAIL>"
  }
}

script:post-response {
  if (res.status === 200 && res.body.data && res.body.data.tenant_id) {
    bru.setEnvVar("tenant_id", res.body.data.tenant_id);
  }
}

tests {
  test("should return 200", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should have tenant data", function() {
    expect(res.body.data.tenant_id).to.be.a('number');
    expect(res.body.data.tenant_name).to.equal('My Test Company');
    expect(res.body.data.tenant_code).to.be.a('string');
    expect(res.body.data.tenant_type).to.equal('company');
  });
  
  test("should have success message", function() {
    expect(res.body.data.message).to.equal('Tenant created successfully');
  });
}
