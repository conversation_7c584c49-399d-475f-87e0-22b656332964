meta {
  name: Health Check
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/admin/v1/onboarding/health
  body: none
  auth: none
}

tests {
  test("should return 200", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should have status ok", function() {
    expect(res.body.status).to.equal('ok');
  });
  
  test("should have module name", function() {
    expect(res.body.module).to.equal('onboarding');
  });
}
