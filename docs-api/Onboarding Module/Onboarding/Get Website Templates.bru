meta {
  name: Get Website Templates
  type: http
  seq: 8
}

get {
  url: {{api_url}}/api/admin/v1/onboarding/templates
  body: none
  auth: bearer
}

auth:bearer {
  token: {{admin_token}}
}

tests {
  test("should return 200", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should have templates array", function() {
    expect(res.body.data.templates).to.be.an('array');
    expect(res.body.data.templates.length).to.be.at.least(1);
  });
  
  test("templates should have required fields", function() {
    res.body.data.templates.forEach(template => {
      expect(template.id).to.be.a('string');
      expect(template.name).to.be.a('string');
      expect(template.description).to.be.a('string');
      expect(template.preview_url).to.be.a('string');
      expect(template.category).to.be.a('string');
    });
  });
}
