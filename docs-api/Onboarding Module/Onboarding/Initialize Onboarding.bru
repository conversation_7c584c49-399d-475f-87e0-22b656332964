meta {
  name: Initialize Onboarding
  type: http
  seq: 5
}

post {
  url: {{api_url}}/api/admin/v1/onboarding/initialize
  body: none
  auth: bearer
}

auth:bearer {
  token: {{admin_token}}
}

headers {
  X-Tenant-ID: {{tenant_id}}
  X-Website-ID: {{website_id}}
}

tests {
  test("should return 200", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should have onboarding status", function() {
    expect(res.body.data.onboarding_status).to.be.a('string');
    expect(res.body.data.progress).to.be.an('array');
    expect(res.body.data.total_steps).to.be.a('number');
    expect(res.body.data.completed_steps).to.be.a('number');
    expect(res.body.data.completion_rate).to.be.a('number');
  });
}
