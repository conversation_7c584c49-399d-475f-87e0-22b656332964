meta {
  name: Get Tenant Name Suggestions
  type: http
  seq: 9
}

get {
  url: {{api_url}}/api/admin/v1/onboarding/suggestions/tenant-name?full_name=<PERSON>
  body: none
  auth: bearer
}

auth:bearer {
  token: {{admin_token}}
}

params:query {
  full_name: <PERSON>
}

tests {
  test("should return 200", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should have suggestions array", function() {
    expect(res.body.data.suggestions).to.be.an('array');
    expect(res.body.data.suggestions.length).to.be.at.least(1);
  });
  
  test("suggestions should be strings", function() {
    res.body.data.suggestions.forEach(suggestion => {
      expect(suggestion).to.be.a('string');
      expect(suggestion.length).to.be.at.least(3);
    });
  });
}
