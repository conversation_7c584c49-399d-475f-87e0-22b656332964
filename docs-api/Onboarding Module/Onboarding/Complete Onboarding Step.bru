meta {
  name: Complete Onboarding Step
  type: http
  seq: 7
}

post {
  url: {{api_url}}/api/admin/v1/onboarding/steps/complete
  body: json
  auth: bearer
}

auth:bearer {
  token: {{admin_token}}
}

headers {
  X-Tenant-ID: {{tenant_id}}
  X-Website-ID: {{website_id}}
}

body:json {
  {
    "step_name": "tenant_setup",
    "data": {
      "tenant_id": "{{tenant_id}}",
      "completed_at": "{{new Date().toISOString()}}"
    }
  }
}

tests {
  test("should return 200", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should have next step info", function() {
    expect(res.body.data.is_completed).to.be.a('boolean');
    expect(res.body.data.message).to.be.a('string');
  });
}
