package handlers

import (
	"net/http"
	"strings"

	"wnapi/internal/pkg/response"
	"wnapi/modules/auth/dto"
	errors "wnapi/modules/auth/internal"
	"wnapi/modules/auth/service"

	"github.com/gin-gonic/gin"
)

// AdminEmailVerificationHandler xử lý các yêu cầu xác thực email cho admin
type AdminEmailVerificationHandler struct {
	authService service.AuthService
}

// NewAdminEmailVerificationHandler tạo một instance mới của AdminEmailVerificationHandler
func NewAdminEmailVerificationHandler(authService service.AuthService) *AdminEmailVerificationHandler {
	return &AdminEmailVerificationHandler{
		authService: authService,
	}
}

// VerifyEmail xử lý việc xác thực email thông qua token
func (h *AdminEmailVerificationHandler) VerifyEmail(c *gin.Context) {
	var req dto.VerifyEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.<PERSON>rror()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Định dạng yêu cầu không hợp lệ", string(errors.ErrCodeValidationFailed), details)
		return
	}

	resp, err := h.authService.VerifyEmail(c.Request.Context(), req.Token)
	if err != nil {
		// Kiểm tra loại lỗi dựa trên thông báo
		if appErr, ok := err.(*errors.AppError); ok {
			response.Error(c, appErr.HTTPStatus, appErr.Message, string(appErr.Code))
			return
		}

		// Kiểm tra lỗi dựa trên message (tạm thời cho đến khi tất cả lỗi đã chuyển sang errors.AppError)
		errMsg := err.Error()
		if strings.Contains(errMsg, "token không hợp lệ") || strings.Contains(errMsg, "token đã hết hạn") {
			response.Error(c, http.StatusNotFound, "Mã xác thực không hợp lệ hoặc đã hết hạn", string(errors.ErrCodeInvalidVerificationToken))
		} else {
			response.Error(c, http.StatusInternalServerError, "Không thể xác thực email", string(errors.ErrCodeEmailVerificationFailed))
		}
		return
	}

	response.Success(c, resp, nil)
}

// ResendVerification xử lý việc gửi lại email xác thực
func (h *AdminEmailVerificationHandler) ResendVerification(c *gin.Context) {
	var req dto.ResendVerificationEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Định dạng yêu cầu không hợp lệ", string(errors.ErrCodeValidationFailed), details)
		return
	}

	resp, err := h.authService.ResendVerificationEmail(c.Request.Context(), req.Email)
	if err != nil {
		// Kiểm tra loại lỗi dựa trên thông báo
		if appErr, ok := err.(*errors.AppError); ok {
			response.Error(c, appErr.HTTPStatus, appErr.Message, string(appErr.Code))
			return
		}

		// Kiểm tra lỗi dựa trên message (tạm thời cho đến khi tất cả lỗi đã chuyển sang errors.AppError)
		errMsg := err.Error()
		if strings.Contains(errMsg, "không tìm thấy người dùng") {
			response.Error(c, http.StatusNotFound, "Không tìm thấy người dùng với email này", string(errors.ErrCodeUserNotFound))
		} else {
			response.Error(c, http.StatusInternalServerError, "Không thể gửi lại email xác thực", string(errors.ErrCodeEmailVerificationFailed))
		}
		return
	}

	response.Success(c, resp, nil)
}
