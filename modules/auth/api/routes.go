package api

import (
	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/internal/middleware"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/api/handlers"
	"wnapi/modules/auth/internal"
	authInternalService "wnapi/modules/auth/internal/service"
	"wnapi/modules/auth/repository/mysql"
	"wnapi/modules/auth/service"
)

// Handler là đối tượng chính xử lý API cho module Auth
type Handler struct {
	// Handlers
	adminAuthHandler              *handlers.AdminAuthHandler
	adminPasswordResetHandler     *handlers.AdminPasswordResetHandler
	adminEmailVerificationHandler *handlers.AdminEmailVerificationHandler
	adminUserHandler              *handlers.AdminUserHandler
	userTenantHandler             *handlers.UserTenantHandler

	// Dependencies
	middlewareFactory *permission.MiddlewareFactory
	jwtService        *auth.JWTService
	logger            logger.Logger
	db                *sqlx.DB
	gormDB            *gorm.DB
	tenantService     middleware.TenantService // Thêm tenant service
}

// NewHandler tạo một handler mới (original signature for module.go compatibility)
func NewHandler(authService service.AuthService, passwordResetService service.PasswordResetService, jwtService *auth.JWTService, repo internal.Repository, config internal.AuthConfig, logger logger.Logger) *Handler {

	// Tạo user service cho CRUD operations
	// Create user service directly using internal service
	userService := authInternalService.NewUserService(repo, config, logger)

	return &Handler{
		adminAuthHandler:              handlers.NewAdminAuthHandler(authService, userService),
		adminPasswordResetHandler:     handlers.NewAdminPasswordResetHandler(passwordResetService),
		adminEmailVerificationHandler: handlers.NewAdminEmailVerificationHandler(authService),
		adminUserHandler:              handlers.NewAdminUserHandler(userService),
		userTenantHandler:             handlers.NewUserTenantHandler(userService),
		middlewareFactory:             nil, // Will be set later if needed
		jwtService:                    jwtService,
		logger:                        logger,
		db:                            nil, // Not needed for original signature
		gormDB:                        nil, // Not needed for original signature
	}
}

// NewHandlerWithDependencies tạo một handler mới với dependencies (fx-compatible signature)
func NewHandlerWithDependencies(
	authService service.AuthService,
	userService service.UserService,
	passwordResetService service.PasswordResetService,
	mwFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	log logger.Logger,
	db *sqlx.DB,
	gormDB *gorm.DB,
	tenantService middleware.TenantService,
) *Handler {
	// Create sub-handlers
	adminAuthHandler := handlers.NewAdminAuthHandler(authService, userService)
	adminPasswordResetHandler := handlers.NewAdminPasswordResetHandler(passwordResetService)
	adminEmailVerificationHandler := handlers.NewAdminEmailVerificationHandler(authService)
	adminUserHandler := handlers.NewAdminUserHandler(userService)
	userTenantHandler := handlers.NewUserTenantHandler(userService)

	return &Handler{
		adminAuthHandler:              adminAuthHandler,
		adminPasswordResetHandler:     adminPasswordResetHandler,
		adminEmailVerificationHandler: adminEmailVerificationHandler,
		adminUserHandler:              adminUserHandler,
		userTenantHandler:             userTenantHandler,
		middlewareFactory:             mwFactory,
		jwtService:                    jwtService,
		logger:                        log,
		db:                            db,
		gormDB:                        gormDB,
		tenantService:                 tenantService,
	}
}

// NewHandlerWithDependenciesOld tạo một handler mới với dependencies (old signature for backward compatibility)
func NewHandlerWithDependenciesOld(db *sqlx.DB, gormDB *gorm.DB, mwFactory *permission.MiddlewareFactory, jwtConfig auth.JWTConfig, log logger.Logger) *Handler {
	// Initialize repositories using the existing constructor
	repo, err := mysql.NewMySQLRepositoryWithDB(gormDB, log)
	if err != nil {
		log.Error("Failed to initialize repository", "error", err)
		return nil
	}

	// Initialize auth config from environment
	config := &internal.AuthConfig{
		// These will be set from environment variables in the actual implementation
		// For now, using defaults
		MaxSessionsPerUser: 10,
	}

	// Initialize services using the new internal service constructors
	authService := authInternalService.NewServiceWithNotification(repo, *config, log, nil, nil, "")

	// For password reset service, we need the proper repositories
	// For now, we'll create a minimal service - this should be improved
	passwordResetRepo := mysql.NewPasswordResetRepository(gormDB, log)
	passwordResetService := service.NewPasswordResetService(passwordResetRepo, nil, "")

	userService := authInternalService.NewUserService(repo, *config, log)

	// Initialize JWT service
	jwtService := auth.NewJWTService(jwtConfig)

	// Initialize handlers
	adminAuthHandler := handlers.NewAdminAuthHandler(authService, userService)
	adminPasswordResetHandler := handlers.NewAdminPasswordResetHandler(passwordResetService)
	adminEmailVerificationHandler := handlers.NewAdminEmailVerificationHandler(authService)
	adminUserHandler := handlers.NewAdminUserHandler(userService)
	userTenantHandler := handlers.NewUserTenantHandler(userService)

	// Cần thêm tenantService vào đây - trong trường hợp thực tế, bạn sẽ cần inject service này
	// Đây chỉ là giải pháp tạm thời, trong môi trường thực tế bạn nên inject service này đúng cách
	// Hoặc cập nhật signature của hàm để nhận tenantService
	tenantService := middleware.NewNoOpTenantService() // Tạm thời sử dụng một implementation giả

	return &Handler{
		adminAuthHandler:              adminAuthHandler,
		adminPasswordResetHandler:     adminPasswordResetHandler,
		adminEmailVerificationHandler: adminEmailVerificationHandler,
		adminUserHandler:              adminUserHandler,
		userTenantHandler:             userTenantHandler,
		middlewareFactory:             mwFactory,
		jwtService:                    jwtService,
		logger:                        log,
		db:                            db,
		gormDB:                        gormDB,
		tenantService:                 tenantService,
	}
}

// RegisterRoutes đăng ký tất cả routes cho module Auth
func (h *Handler) RegisterRoutes(router *gin.Engine) error {
	// Nhóm routes cho API auth
	authGroup := router.Group("/api/v1/auth")
	{
		// Public routes
		authGroup.POST("/login", h.adminAuthHandler.Login)
		authGroup.POST("/register", h.adminAuthHandler.Register)
		authGroup.POST("/refresh-token", h.adminAuthHandler.RefreshToken)
		authGroup.POST("/verify-email", h.adminEmailVerificationHandler.VerifyEmail)
		authGroup.POST("/resend-verification", h.adminEmailVerificationHandler.ResendVerification)
		authGroup.POST("/forgot-password", h.adminPasswordResetHandler.ForgotPassword)
		authGroup.POST("/reset-password", h.adminPasswordResetHandler.ResetPassword)

		// Protected routes
		protectedAuth := authGroup.Group("/")
		protectedAuth.Use(h.jwtService.JWTAuthMiddleware())
		{
			protectedAuth.POST("/logout", h.adminAuthHandler.Logout)
			protectedAuth.GET("/profile", h.adminAuthHandler.GetProfile)
			protectedAuth.PUT("/profile", h.adminAuthHandler.UpdateProfile)
			protectedAuth.PUT("/change-password", h.adminAuthHandler.ChangePassword)
		}
	}

	// Nhóm routes cho quản lý users
	usersGroup := router.Group("/api/v1/users")
	{
		// Protected routes
		protectedUsers := usersGroup.Group("/")

		// Sửa đổi middleware
		protectedUsers.Use(
			h.jwtService.JWTAuthMiddleware(),
			middleware.TenantMiddleware(h.tenantService), // Sử dụng TenantMiddleware
		)
		{
			protectedUsers.GET("/me", h.adminAuthHandler.GetMe)

			// User management routes with permission middleware
			protectedUsers.GET("/",
				h.middlewareFactory.RequirePermission(internal.ListUserPermission),
				h.adminUserHandler.ListUsers,
			)
			protectedUsers.GET("/:id",
				h.middlewareFactory.RequirePermission(internal.ReadUserPermission),
				h.adminUserHandler.GetUser,
			)
			protectedUsers.POST("/",
				h.middlewareFactory.RequirePermission(internal.CreateUserPermission),
				h.adminUserHandler.CreateUser,
			)
			protectedUsers.PUT("/:id",
				h.middlewareFactory.RequirePermission(internal.UpdateUserPermission),
				h.adminUserHandler.UpdateUser,
			)
			protectedUsers.DELETE("/:id",
				h.middlewareFactory.RequirePermission(internal.DeleteUserPermission),
				h.adminUserHandler.DeleteUser,
			)

			// User-Tenant relationship routes
			protectedUsers.GET("/me/tenants",
				h.middlewareFactory.RequirePermission(internal.ReadUserPermission),
				h.userTenantHandler.GetUserTenants,
			)
		}
	}

	return nil
}

// RegisterAuthRoutes đăng ký routes cho authentication
func (h *Handler) RegisterAuthRoutes(router *gin.Engine) error {
	// Auth API Group
	authGroup := router.Group("/api/admin/v1/auth")
	authGroup.Use(tracing.GinMiddleware("auth"))

	// Health check endpoint
	authGroup.GET("/health", h.healthCheck)

	// ===== BASIC AUTH ROUTES =====
	// Public endpoints - không yêu cầu xác thực
	authGroup.POST("/login", h.adminAuthHandler.Login)
	authGroup.POST("/signin", h.adminAuthHandler.Login) // Alias for login
	authGroup.POST("/register", h.adminAuthHandler.Register)
	authGroup.POST("/signup", h.adminAuthHandler.Register) // Alias for register
	authGroup.POST("/refresh-token", h.adminAuthHandler.RefreshToken)

	// ===== PASSWORD RESET ROUTES =====
	authGroup.POST("/forgot-password", h.adminPasswordResetHandler.ForgotPassword)
	authGroup.GET("/verify-reset-token", h.adminPasswordResetHandler.VerifyResetToken)
	authGroup.POST("/reset-password", h.adminPasswordResetHandler.ResetPassword)

	// ===== EMAIL VERIFICATION ROUTES =====
	authGroup.POST("/verify-email", h.adminEmailVerificationHandler.VerifyEmail)
	authGroup.POST("/resend-verification", h.adminEmailVerificationHandler.ResendVerification)

	// Protected endpoints - require JWT authentication
	protectedAuth := authGroup.Group("")
	protectedAuth.Use(h.jwtService.JWTAuthMiddleware())
	{
		protectedAuth.POST("/logout", h.adminAuthHandler.Logout)
		protectedAuth.PUT("/change-password", h.adminAuthHandler.ChangePassword)
		protectedAuth.GET("/profile", h.adminAuthHandler.GetProfile)
	}

	return nil
}

// RegisterUsersRoutes đăng ký routes cho user management
func (h *Handler) RegisterUsersRoutes(router *gin.Engine) error {
	// Users API Group
	usersGroup := router.Group("/api/admin/v1/users")
	usersGroup.Use(tracing.GinMiddleware("users"))

	// Protected routes - require JWT authentication and permissions
	protectedUsers := usersGroup.Group("")
	protectedUsers.Use(h.jwtService.JWTAuthMiddleware())
	{
		protectedUsers.GET("/me", h.adminAuthHandler.GetMe)

		// User management routes with permission middleware
		protectedUsers.GET("/",
			h.middlewareFactory.RequirePermission(internal.ListUserPermission),
			h.adminUserHandler.ListUsers,
		)
		protectedUsers.GET("/:id",
			h.middlewareFactory.RequirePermission(internal.ReadUserPermission),
			h.adminUserHandler.GetUser,
		)
		protectedUsers.POST("/",
			h.middlewareFactory.RequirePermission(internal.CreateUserPermission),
			h.adminUserHandler.CreateUser,
		)
		protectedUsers.PUT("/:id",
			h.middlewareFactory.RequirePermission(internal.UpdateUserPermission),
			h.adminUserHandler.UpdateUser,
		)
		protectedUsers.DELETE("/:id",
			h.middlewareFactory.RequirePermission(internal.DeleteUserPermission),
			h.adminUserHandler.DeleteUser,
		)

		// User-Tenant relationship routes
		protectedUsers.GET("/me/tenants",
			h.middlewareFactory.RequirePermission(internal.ReadUserPermission),
			h.userTenantHandler.GetUserTenants,
		)
	}

	return nil
}

// RegisterRoutesWithServer đăng ký tất cả routes cho module Auth với core.Server (for module.go compatibility)
func (h *Handler) RegisterRoutesWithServer(server interface{}) error {
	// Type assertion to handle both *core.Server and *gin.Engine
	switch s := server.(type) {
	case *gin.Engine:
		return h.RegisterRoutes(s)
	default:
		// Assume it's a *core.Server and get the router
		if srv, ok := server.(interface{ GetRouter() *gin.Engine }); ok {
			return h.RegisterRoutes(srv.GetRouter())
		}
		h.logger.Error("Unsupported server type for route registration")
		return nil
	}
}

// SetMiddlewareFactory sets the middleware factory for permission-based routes
func (h *Handler) SetMiddlewareFactory(mwFactory *permission.MiddlewareFactory) {
	h.middlewareFactory = mwFactory
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "auth",
		"message": "Auth module is running",
	})
}

// RegisterRoutes registers all API routes for the auth module (backward compatibility)
func RegisterRoutes(router *gin.Engine, db *sqlx.DB, gormDB *gorm.DB, mwFactory *permission.MiddlewareFactory, jwtConfig auth.JWTConfig, log logger.Logger) {
	handler := NewHandlerWithDependenciesOld(db, gormDB, mwFactory, jwtConfig, log)
	if handler != nil {
		handler.RegisterRoutes(router)
	}
}

// SetupRoutes sets up all the auth module routes (for backward compatibility)
func SetupRoutes(router *gin.Engine, db *sqlx.DB, gormDB *gorm.DB, mwFactory *permission.MiddlewareFactory, jwtConfig auth.JWTConfig, log logger.Logger) {
	RegisterRoutes(router, db, gormDB, mwFactory, jwtConfig, log)
}

// AuthHandler is an alias for Handler to maintain backward compatibility
type AuthHandler = Handler

// NewAuthHandler creates a new AuthHandler (alias for NewHandler) - DEPRECATED
// Use NewHandler instead with the new signature
func NewAuthHandler(authService service.AuthService, passwordResetService service.PasswordResetService, jwtConfig auth.JWTConfig, repo internal.Repository, config internal.AuthConfig, logger logger.Logger) *AuthHandler {
	// For backward compatibility, still create the handler but log deprecation warning
	logger.Warn("NewAuthHandler is deprecated, use NewHandler with new signature instead")
	// Create JWT service from config for backward compatibility
	jwtService := auth.NewJWTService(jwtConfig)
	return NewHandler(authService, passwordResetService, jwtService, repo, config, logger)
}
