package onboarding

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"wnapi/internal/pkg/auth"
	"wnapi/modules/onboarding/api"
	"wnapi/modules/onboarding/dto"
	"wnapi/modules/onboarding/models"
	"wnapi/modules/onboarding/repository/mysql"
	"wnapi/modules/onboarding/service"
)

// TestOnboardingModuleIntegration tests the basic integration of the onboarding module
func TestOnboardingModuleIntegration(t *testing.T) {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	// Auto migrate tables
	err = db.AutoMigrate(&models.UserOnboardingProgress{})
	assert.NoError(t, err)

	// Setup dependencies
	repo := mysql.NewOnboardingRepository(db)
	// For testing, we'll pass nil for tenant service and logger
	onboardingService := service.NewOnboardingService(repo, nil, nil, db)

	// Create JWT service for testing
	jwtConfig := auth.JWTConfig{
		AccessSigningKey:       "test-access-key",
		RefreshSigningKey:      "test-refresh-key",
		AccessTokenExpiration:  time.Hour,
		RefreshTokenExpiration: 24 * time.Hour,
		Issuer:                 "test-issuer",
	}
	jwtService := auth.NewJWTService(jwtConfig)

	// Create handler
	handler := api.NewHandler(onboardingService, jwtService, nil, db)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Register routes
	err = handler.RegisterRoutes(router)
	assert.NoError(t, err)

	// Test data
	userID := uint(1)
	tenantID := "test-tenant"
	websiteID := "test-website"

	t.Run("Initialize Onboarding", func(t *testing.T) {
		// Create test request
		req, err := http.NewRequest("POST", "/api/admin/v1/onboarding/initialize", nil)
		assert.NoError(t, err)

		// Add headers
		req.Header.Set("X-Tenant-ID", tenantID)
		req.Header.Set("X-Website-ID", websiteID)

		// Create response recorder
		w := httptest.NewRecorder()

		// Add user context (simulate JWT middleware)
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("userID", userID)

		// This would normally be called by the router, but we'll test the service directly
		status, err := onboardingService.InitializeOnboardingProgress(c.Request.Context(), userID, tenantID, websiteID)
		assert.NoError(t, err)
		assert.NotNil(t, status)
		assert.Equal(t, "in_progress", status.OnboardingStatus)
		assert.Equal(t, 5, status.TotalSteps)
		assert.Equal(t, 0, status.CompletedSteps)
	})

	t.Run("Get Onboarding Status", func(t *testing.T) {
		status, err := onboardingService.GetOnboardingStatus(nil, userID, tenantID, websiteID)
		assert.NoError(t, err)
		assert.NotNil(t, status)
		assert.Equal(t, "in_progress", status.OnboardingStatus)
		assert.Len(t, status.Progress, 5)
	})

	t.Run("Complete Onboarding Step", func(t *testing.T) {
		req := &dto.CompleteOnboardingStepRequest{
			StepName: models.OnboardingStepEmailVerification,
			Data:     map[string]interface{}{"verified": true},
		}

		nextStep, err := onboardingService.CompleteOnboardingStep(nil, userID, tenantID, websiteID, req)
		assert.NoError(t, err)
		assert.NotNil(t, nextStep)
		assert.Equal(t, models.OnboardingStepTenantSetup, *nextStep.NextStep)
		assert.False(t, nextStep.IsCompleted)
	})

	t.Run("Skip Onboarding Step", func(t *testing.T) {
		req := &dto.SkipOnboardingStepRequest{
			StepName: models.OnboardingStepProfileSetup,
			Reason:   "Will do later",
		}

		nextStep, err := onboardingService.SkipOnboardingStep(nil, userID, tenantID, websiteID, req)
		assert.NoError(t, err)
		assert.NotNil(t, nextStep)
	})

	t.Run("Get Next Step", func(t *testing.T) {
		nextStep, err := onboardingService.GetNextOnboardingStep(nil, userID, tenantID, websiteID)
		assert.NoError(t, err)
		assert.NotNil(t, nextStep)
		assert.False(t, nextStep.IsCompleted)
	})

	t.Run("Get Tenant Name Suggestions", func(t *testing.T) {
		suggestions, err := onboardingService.GetTenantNameSuggestions(nil, "John Doe")
		assert.NoError(t, err)
		assert.NotNil(t, suggestions)
		assert.Greater(t, len(suggestions.Suggestions), 0)
	})

	t.Run("Get Website Templates", func(t *testing.T) {
		templates, err := onboardingService.GetWebsiteTemplates(nil)
		assert.NoError(t, err)
		assert.NotNil(t, templates)
		assert.Greater(t, len(templates.Templates), 0)
	})
}

// TestOnboardingAPIEndpoints tests the API endpoints with HTTP requests
func TestOnboardingAPIEndpoints(t *testing.T) {
	// This test would require a more complex setup with actual HTTP server
	// For now, we'll just verify that the module can be instantiated correctly

	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	// Auto migrate tables
	err = db.AutoMigrate(&models.UserOnboardingProgress{})
	assert.NoError(t, err)

	// Verify module can be created
	module := &OnboardingModule{}
	assert.Equal(t, "onboarding", module.Name())
	assert.Equal(t, 20, module.Priority())
	assert.True(t, module.Enabled(map[string]interface{}{}))
	assert.Equal(t, "modules/onboarding/migrations", module.GetMigrationPath())
	assert.Equal(t, 20, module.GetMigrationOrder())
	assert.Contains(t, module.Dependencies(), "auth")
	assert.Contains(t, module.Dependencies(), "tenant")
}

// Helper function to create test JSON request
func createJSONRequest(method, url string, data interface{}) (*http.Request, error) {
	var buf bytes.Buffer
	if data != nil {
		if err := json.NewEncoder(&buf).Encode(data); err != nil {
			return nil, err
		}
	}

	req, err := http.NewRequest(method, url, &buf)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	return req, nil
}

// Helper function to parse JSON response
func parseJSONResponse(w *httptest.ResponseRecorder, target interface{}) error {
	return json.NewDecoder(w.Body).Decode(target)
}
