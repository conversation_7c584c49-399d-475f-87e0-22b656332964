package repository

import (
	"context"
	"wnapi/modules/onboarding/models"
)

// Repository interface định nghĩa các phương thức truy cập dữ liệu cho onboarding
type Repository interface {
	// OnboardingProgress operations
	CreateOnboardingProgress(ctx context.Context, progress *models.UserOnboardingProgress) error
	GetOnboardingProgressByUser(ctx context.Context, userID, tenantID, websiteID uint) ([]*models.UserOnboardingProgress, error)
	GetOnboardingProgressByUserAndStep(ctx context.Context, userID, tenantID, websiteID uint, stepName string) (*models.UserOnboardingProgress, error)
	UpdateOnboardingProgress(ctx context.Context, progress *models.UserOnboardingProgress) error
	DeleteOnboardingProgress(ctx context.Context, userID, tenantID, websiteID uint, stepName string) error
	UpsertOnboardingProgress(ctx context.Context, progress *models.UserOnboardingProgress) error
}
