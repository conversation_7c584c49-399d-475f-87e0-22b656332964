package models

import (
	"encoding/json"
	"time"
)

// OnboardingStepStatus định nghĩa trạng thái của từng bước onboarding
type OnboardingStepStatus string

const (
	OnboardingStepStatusNotStarted OnboardingStepStatus = "not_started"
	OnboardingStepStatusInProgress OnboardingStepStatus = "in_progress"
	OnboardingStepStatusCompleted  OnboardingStepStatus = "completed"
	OnboardingStepStatusSkipped    OnboardingStepStatus = "skipped"
)

// OnboardingStep định nghĩa các bước onboarding
const (
	OnboardingStepEmailVerification = "email_verification"
	OnboardingStepTenantSetup       = "tenant_setup"
	OnboardingStepWebsiteSetup      = "website_setup"
	OnboardingStepProfileSetup      = "profile_setup"
	OnboardingStepTutorial          = "tutorial"
)

// IsValid kiểm tra tính hợp lệ của step status
func (s OnboardingStepStatus) IsValid() bool {
	switch s {
	case OnboardingStepStatusNotStarted, OnboardingStepStatusInProgress, OnboardingStepStatusCompleted, OnboardingStepStatusSkipped:
		return true
	default:
		return false
	}
}

// String trả về string representation của step status
func (s OnboardingStepStatus) String() string {
	return string(s)
}

// UserOnboardingProgress theo dõi chi tiết tiến trình onboarding của user
type UserOnboardingProgress struct {
	ID          uint                 `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserID      uint                 `gorm:"column:user_id;not null" json:"user_id"`
	TenantID    uint                 `gorm:"column:tenant_id;not null;index" json:"tenant_id"`
	WebsiteID   uint                 `gorm:"column:website_id;not null;index" json:"website_id"`
	StepName    string               `gorm:"column:step_name;not null" json:"step_name"`
	Status      OnboardingStepStatus `gorm:"column:status;type:ENUM('not_started', 'in_progress', 'completed', 'skipped');default:not_started" json:"status"`
	StartedAt   *time.Time           `gorm:"column:started_at" json:"started_at,omitempty"`
	CompletedAt *time.Time           `gorm:"column:completed_at" json:"completed_at,omitempty"`
	Data        json.RawMessage      `gorm:"column:data;type:json" json:"data,omitempty"`
	CreatedAt   time.Time            `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time            `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName chỉ định tên bảng cho model UserOnboardingProgress
func (UserOnboardingProgress) TableName() string {
	return "user_onboarding_progress"
}

// IsCompleted kiểm tra bước đã hoàn thành chưa
func (p *UserOnboardingProgress) IsCompleted() bool {
	return p.Status == OnboardingStepStatusCompleted
}

// IsSkipped kiểm tra bước đã bị bỏ qua chưa
func (p *UserOnboardingProgress) IsSkipped() bool {
	return p.Status == OnboardingStepStatusSkipped
}

// IsInProgress kiểm tra bước đang trong quá trình thực hiện
func (p *UserOnboardingProgress) IsInProgress() bool {
	return p.Status == OnboardingStepStatusInProgress
}

// Start bắt đầu bước onboarding
func (p *UserOnboardingProgress) Start() {
	p.Status = OnboardingStepStatusInProgress
	now := time.Now()
	p.StartedAt = &now
}

// Complete hoàn thành bước onboarding
func (p *UserOnboardingProgress) Complete() {
	p.Status = OnboardingStepStatusCompleted
	now := time.Now()
	p.CompletedAt = &now
}

// Skip bỏ qua bước onboarding
func (p *UserOnboardingProgress) Skip() {
	p.Status = OnboardingStepStatusSkipped
	now := time.Now()
	p.CompletedAt = &now
}

// SetData cập nhật dữ liệu bổ sung cho bước
func (p *UserOnboardingProgress) SetData(data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	p.Data = jsonData
	return nil
}

// GetData lấy dữ liệu bổ sung từ bước
func (p *UserOnboardingProgress) GetData(target interface{}) error {
	if len(p.Data) == 0 {
		return nil
	}
	return json.Unmarshal(p.Data, target)
}

// GetDurationInProgress tính thời gian thực hiện bước (nếu đang in progress)
func (p *UserOnboardingProgress) GetDurationInProgress() *time.Duration {
	if p.StartedAt == nil {
		return nil
	}

	endTime := time.Now()
	if p.CompletedAt != nil {
		endTime = *p.CompletedAt
	}

	duration := endTime.Sub(*p.StartedAt)
	return &duration
}
