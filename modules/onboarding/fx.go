package onboarding

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/fx"
	"gorm.io/gorm"

	"wnapi/internal/fx/modules"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/onboarding/api"
	"wnapi/modules/onboarding/repository"
	"wnapi/modules/onboarding/repository/mysql"
	"wnapi/modules/onboarding/service"
	tenantService "wnapi/modules/tenant/service"
)

// OnboardingModule implements FXModule interface
type OnboardingModule struct{}

// Name returns module name
func (m *OnboardingModule) Name() string {
	return "onboarding"
}

// Dependencies returns module dependencies
func (m *OnboardingModule) Dependencies() []string {
	return []string{"auth", "tenant"} // Depends on auth for JWT and tenant for multi-tenancy
}

// Priority returns loading priority
func (m *OnboardingModule) Priority() int {
	return 20 // Load after auth and tenant modules
}

// Enabled checks if module should be loaded
func (m *OnboardingModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default enabled
}

// GetMigrationPath returns path to module migrations
func (m *OnboardingModule) GetMigrationPath() string {
	return "modules/onboarding/migrations"
}

// GetMigrationOrder returns migration priority order
func (m *OnboardingModule) GetMigrationOrder() int {
	return 20 // Run after auth module migrations
}

// NewOnboardingServiceWithDependencies creates onboarding service with all dependencies
func NewOnboardingServiceWithDependencies(
	repo repository.Repository,
	tenantSvc tenantService.TenantService,
	logger logger.Logger,
	db *gorm.DB,
) service.OnboardingService {
	return service.NewOnboardingService(repo, tenantSvc, logger, db)
}

// Module returns fx.Module for onboarding
func (m *OnboardingModule) Module() fx.Option {
	return fx.Module("onboarding",
		// Providers
		fx.Provide(
			// Repositories
			fx.Annotate(
				mysql.NewOnboardingRepository,
				fx.As(new(repository.Repository)),
			),

			// Services
			fx.Annotate(
				NewOnboardingServiceWithDependencies,
				fx.As(new(service.OnboardingService)),
			),

			// Handlers
			api.NewHandlerWithDependencies,
		),

		// Route registration using FX handler
		fx.Invoke(RegisterOnboardingRoutes),
	)
}

// RegisterOnboardingRoutes registers onboarding routes with the router
func RegisterOnboardingRoutes(handler *api.Handler, router *gin.Engine) {
	if handler != nil {
		handler.RegisterRoutes(router)
	}
}

// Register onboarding module with global registry
func init() {
	modules.RegisterModule(&OnboardingModule{})
}
