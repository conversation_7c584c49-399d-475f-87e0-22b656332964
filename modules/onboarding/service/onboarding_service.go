package service

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/onboarding/dto"
	"wnapi/modules/onboarding/models"
	"wnapi/modules/onboarding/repository"
	tenantDto "wnapi/modules/tenant/dto"
	tenantService "wnapi/modules/tenant/service"
	websiteModels "wnapi/modules/website/models"

	"gorm.io/gorm"
)

// OnboardingService interface cho onboarding service
type OnboardingService interface {
	GetOnboardingStatus(ctx context.Context, userID, tenantID, websiteID uint) (*dto.OnboardingStatusResponse, error)
	StartOnboardingStep(ctx context.Context, userID, tenantID, websiteID uint, req *dto.StartOnboardingStepRequest) error
	CompleteOnboardingStep(ctx context.Context, userID, tenantID, websiteID uint, req *dto.CompleteOnboardingStepRequest) (*dto.OnboardingNextStepResponse, error)
	SkipOnboardingStep(ctx context.Context, userID, tenantID, websiteID uint, req *dto.SkipOnboardingStepRequest) (*dto.OnboardingNextStepResponse, error)
	GetNextOnboardingStep(ctx context.Context, userID, tenantID, websiteID uint) (*dto.OnboardingNextStepResponse, error)
	InitializeOnboardingProgress(ctx context.Context, userID, tenantID, websiteID uint) (*dto.OnboardingStatusResponse, error)
	GetTenantNameSuggestions(ctx context.Context, fullName string) (*dto.TenantNameSuggestionsResponse, error)
	GetWebsiteTemplates(ctx context.Context) (*dto.WebsiteTemplatesResponse, error)

	// New methods for tenant and website creation
	CreateTenant(ctx context.Context, userID uint, req *dto.CreateTenantOnboardingRequest) (*dto.CreateTenantOnboardingResponse, error)
	CreateWebsite(ctx context.Context, userID, tenantID uint, req *dto.CreateWebsiteOnboardingRequest) (*dto.CreateWebsiteOnboardingResponse, error)
	GenerateTenantCode(ctx context.Context, tenantName string) (string, error)
}

// onboardingServiceImpl implementation của OnboardingService
type onboardingServiceImpl struct {
	repo          repository.Repository
	tenantService tenantService.TenantService
	logger        logger.Logger
	db            *gorm.DB
}

// NewOnboardingService tạo instance mới của OnboardingService
func NewOnboardingService(
	repo repository.Repository,
	tenantService tenantService.TenantService,
	logger logger.Logger,
	db *gorm.DB,
) OnboardingService {
	return &onboardingServiceImpl{
		repo:          repo,
		tenantService: tenantService,
		logger:        logger,
		db:            db,
	}
}

// GetOnboardingStatus lấy trạng thái onboarding của user
func (s *onboardingServiceImpl) GetOnboardingStatus(ctx context.Context, userID, tenantID, websiteID uint) (*dto.OnboardingStatusResponse, error) {
	// Lấy danh sách progress
	progresses, err := s.repo.GetOnboardingProgressByUser(ctx, userID, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}

	// Danh sách các bước cần thiết
	requiredSteps := []string{
		models.OnboardingStepEmailVerification,
		models.OnboardingStepTenantSetup,
		models.OnboardingStepWebsiteSetup,
		models.OnboardingStepProfileSetup,
		models.OnboardingStepTutorial,
	}

	// Tạo map để lookup progress
	progressMap := make(map[string]*models.UserOnboardingProgress)
	for _, p := range progresses {
		progressMap[p.StepName] = p
	}

	// Tính toán progress details
	var progressDetails []dto.OnboardingProgressDetail
	completedCount := 0

	for _, stepName := range requiredSteps {
		detail := dto.OnboardingProgressDetail{
			StepName: stepName,
			Status:   string(models.OnboardingStepStatusNotStarted),
		}

		if progress, exists := progressMap[stepName]; exists {
			detail.Status = string(progress.Status)
			detail.StartedAt = progress.StartedAt
			detail.CompletedAt = progress.CompletedAt

			// Parse data if exists
			if len(progress.Data) > 0 {
				detail.Data = progress.Data
			}

			// Count completed steps
			if progress.Status == models.OnboardingStepStatusCompleted {
				completedCount++
			}
		}

		progressDetails = append(progressDetails, detail)
	}

	totalSteps := len(requiredSteps)
	completionRate := float64(completedCount) / float64(totalSteps) * 100

	// Determine overall status
	onboardingStatus := "not_started"
	var currentStep *string

	if completedCount == totalSteps {
		onboardingStatus = "completed"
	} else if completedCount > 0 {
		onboardingStatus = "in_progress"
		// Find current step
		for _, stepName := range requiredSteps {
			if progress, exists := progressMap[stepName]; !exists || progress.Status == models.OnboardingStepStatusNotStarted {
				currentStep = &stepName
				break
			}
		}
	} else {
		currentStep = &requiredSteps[0] // Start with first step
	}

	return &dto.OnboardingStatusResponse{
		OnboardingStatus: onboardingStatus,
		OnboardingStep:   currentStep,
		Progress:         progressDetails,
		CompletedSteps:   completedCount,
		TotalSteps:       totalSteps,
		CompletionRate:   completionRate,
	}, nil
}

// StartOnboardingStep bắt đầu một bước onboarding
func (s *onboardingServiceImpl) StartOnboardingStep(ctx context.Context, userID, tenantID, websiteID uint, req *dto.StartOnboardingStepRequest) error {
	// Kiểm tra xem step đã tồn tại chưa
	existingProgress, err := s.repo.GetOnboardingProgressByUserAndStep(ctx, userID, tenantID, websiteID, req.StepName)
	if err != nil && !errors.Is(err, errors.New("record not found")) {
		return fmt.Errorf("failed to check existing progress: %w", err)
	}

	if existingProgress != nil {
		// Nếu đã completed hoặc skipped, không cho start lại
		if existingProgress.Status == models.OnboardingStepStatusCompleted ||
			existingProgress.Status == models.OnboardingStepStatusSkipped {
			return errors.New("step already completed or skipped")
		}

		// Nếu đã in progress, cập nhật data
		existingProgress.Start()
		if req.Data != nil {
			err = existingProgress.SetData(req.Data)
			if err != nil {
				return fmt.Errorf("failed to set data: %w", err)
			}
		}
		return s.repo.UpdateOnboardingProgress(ctx, existingProgress)
	}

	// Tạo progress mới
	progress := &models.UserOnboardingProgress{
		UserID:    userID,
		TenantID:  tenantID,
		WebsiteID: websiteID,
		StepName:  req.StepName,
	}
	progress.Start()

	if req.Data != nil {
		err = progress.SetData(req.Data)
		if err != nil {
			return fmt.Errorf("failed to set data: %w", err)
		}
	}

	return s.repo.CreateOnboardingProgress(ctx, progress)
}

// CompleteOnboardingStep hoàn thành một bước onboarding
func (s *onboardingServiceImpl) CompleteOnboardingStep(ctx context.Context, userID, tenantID, websiteID uint, req *dto.CompleteOnboardingStepRequest) (*dto.OnboardingNextStepResponse, error) {
	// Lấy existing progress
	progress, err := s.repo.GetOnboardingProgressByUserAndStep(ctx, userID, tenantID, websiteID, req.StepName)
	if err != nil {
		// Nếu chưa có, tạo mới và complete luôn
		progress = &models.UserOnboardingProgress{
			UserID:    userID,
			TenantID:  tenantID,
			WebsiteID: websiteID,
			StepName:  req.StepName,
		}
		progress.Start()
	}

	// Complete step
	progress.Complete()

	// Set data if provided
	if req.Data != nil {
		err = progress.SetData(req.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to set data: %w", err)
		}
	}

	// Save progress
	if progress.ID == 0 {
		err = s.repo.CreateOnboardingProgress(ctx, progress)
	} else {
		err = s.repo.UpdateOnboardingProgress(ctx, progress)
	}
	if err != nil {
		return nil, fmt.Errorf("failed to save progress: %w", err)
	}

	// TODO: Process step-specific logic here (create tenant, website, etc.)

	// Get next step
	return s.GetNextOnboardingStep(ctx, userID, tenantID, websiteID)
}

// SkipOnboardingStep bỏ qua một bước onboarding
func (s *onboardingServiceImpl) SkipOnboardingStep(ctx context.Context, userID, tenantID, websiteID uint, req *dto.SkipOnboardingStepRequest) (*dto.OnboardingNextStepResponse, error) {
	// Kiểm tra step có thể skip không
	if req.StepName == models.OnboardingStepEmailVerification {
		return nil, errors.New("email verification step cannot be skipped")
	}
	if req.StepName == models.OnboardingStepTenantSetup {
		return nil, errors.New("tenant setup step cannot be skipped")
	}

	// Lấy existing progress
	progress, err := s.repo.GetOnboardingProgressByUserAndStep(ctx, userID, tenantID, websiteID, req.StepName)
	if err != nil {
		// Nếu chưa có, tạo mới và skip luôn
		progress = &models.UserOnboardingProgress{
			UserID:    userID,
			TenantID:  tenantID,
			WebsiteID: websiteID,
			StepName:  req.StepName,
		}
	}

	// Skip step
	progress.Skip()

	// Set reason if provided
	if req.Reason != "" {
		skipData := map[string]string{"reason": req.Reason}
		err = progress.SetData(skipData)
		if err != nil {
			return nil, fmt.Errorf("failed to set skip reason: %w", err)
		}
	}

	// Save progress
	if progress.ID == 0 {
		err = s.repo.CreateOnboardingProgress(ctx, progress)
	} else {
		err = s.repo.UpdateOnboardingProgress(ctx, progress)
	}
	if err != nil {
		return nil, fmt.Errorf("failed to save progress: %w", err)
	}

	// Get next step
	return s.GetNextOnboardingStep(ctx, userID, tenantID, websiteID)
}

// GetNextOnboardingStep lấy bước onboarding tiếp theo
func (s *onboardingServiceImpl) GetNextOnboardingStep(ctx context.Context, userID, tenantID, websiteID uint) (*dto.OnboardingNextStepResponse, error) {
	status, err := s.GetOnboardingStatus(ctx, userID, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get onboarding status: %w", err)
	}

	if status.OnboardingStatus == "completed" {
		return &dto.OnboardingNextStepResponse{
			NextStep:    nil,
			IsCompleted: true,
			Message:     "Onboarding completed successfully",
		}, nil
	}

	if status.OnboardingStep == nil {
		return &dto.OnboardingNextStepResponse{
			NextStep:    nil,
			IsCompleted: false,
			Message:     "No next step available",
		}, nil
	}

	return &dto.OnboardingNextStepResponse{
		NextStep:    status.OnboardingStep,
		IsCompleted: false,
		Message:     fmt.Sprintf("Next step: %s", *status.OnboardingStep),
	}, nil
}

// InitializeOnboardingProgress khởi tạo progress cho tất cả steps
func (s *onboardingServiceImpl) InitializeOnboardingProgress(ctx context.Context, userID, tenantID, websiteID uint) (*dto.OnboardingStatusResponse, error) {
	requiredSteps := []string{
		models.OnboardingStepEmailVerification,
		models.OnboardingStepTenantSetup,
		models.OnboardingStepWebsiteSetup,
		models.OnboardingStepProfileSetup,
		models.OnboardingStepTutorial,
	}

	for _, stepName := range requiredSteps {
		// Kiểm tra xem step đã tồn tại chưa
		_, err := s.repo.GetOnboardingProgressByUserAndStep(ctx, userID, tenantID, websiteID, stepName)
		if err == nil {
			continue // Step đã tồn tại, skip
		}

		// Tạo progress mới với status not_started
		progress := &models.UserOnboardingProgress{
			UserID:    userID,
			TenantID:  tenantID,
			WebsiteID: websiteID,
			StepName:  stepName,
			Status:    models.OnboardingStepStatusNotStarted,
		}

		err = s.repo.CreateOnboardingProgress(ctx, progress)
		if err != nil {
			return nil, fmt.Errorf("failed to create progress for step %s: %w", stepName, err)
		}
	}

	// Return initial status
	return s.GetOnboardingStatus(ctx, userID, tenantID, websiteID)
}

// GetTenantNameSuggestions tạo gợi ý tên tenant từ tên đầy đủ
func (s *onboardingServiceImpl) GetTenantNameSuggestions(ctx context.Context, fullName string) (*dto.TenantNameSuggestionsResponse, error) {
	if fullName == "" {
		return &dto.TenantNameSuggestionsResponse{Suggestions: []string{}}, nil
	}

	// Tạo các gợi ý từ tên đầy đủ
	suggestions := []string{}

	// Normalize name
	name := strings.ToLower(strings.TrimSpace(fullName))
	name = strings.ReplaceAll(name, " ", "-")

	// Basic suggestions
	suggestions = append(suggestions, name+"-company")
	suggestions = append(suggestions, name+"-corp")
	suggestions = append(suggestions, name+"-inc")

	// Remove dashes for some variations
	noDash := strings.ReplaceAll(name, "-", "")
	if noDash != name {
		suggestions = append(suggestions, noDash+"corp")
		suggestions = append(suggestions, noDash+"inc")
	}

	return &dto.TenantNameSuggestionsResponse{Suggestions: suggestions}, nil
}

// GetWebsiteTemplates lấy danh sách mẫu website
func (s *onboardingServiceImpl) GetWebsiteTemplates(ctx context.Context) (*dto.WebsiteTemplatesResponse, error) {
	// TODO: Implement actual template fetching from database
	// For now, return mock data
	templates := []dto.WebsiteTemplate{
		{
			ID:          "ecommerce_basic",
			Name:        "E-commerce Basic",
			Description: "A basic template for online stores.",
			PreviewURL:  "https://example.com/preview/ecommerce_basic",
			Category:    "ecommerce",
		},
		{
			ID:          "blog_simple",
			Name:        "Simple Blog",
			Description: "A clean and simple blog template.",
			PreviewURL:  "https://example.com/preview/blog_simple",
			Category:    "blog",
		},
		{
			ID:          "business_corporate",
			Name:        "Corporate Business",
			Description: "Professional template for business websites.",
			PreviewURL:  "https://example.com/preview/business_corporate",
			Category:    "business",
		},
	}

	return &dto.WebsiteTemplatesResponse{Templates: templates}, nil
}

// CreateTenant tạo tenant mới trong quá trình onboarding
func (s *onboardingServiceImpl) CreateTenant(ctx context.Context, userID uint, req *dto.CreateTenantOnboardingRequest) (*dto.CreateTenantOnboardingResponse, error) {
	s.logger.Info("Creating tenant during onboarding", "user_id", userID, "tenant_name", req.TenantName)

	// Generate tenant code if not provided
	tenantCode, err := s.GenerateTenantCode(ctx, req.TenantName)
	if err != nil {
		s.logger.Error("Failed to generate tenant code", "error", err.Error())
		return nil, fmt.Errorf("failed to generate tenant code: %w", err)
	}

	// Create tenant request for the tenant service
	createTenantReq := tenantDto.CreateTenantRequest{
		TenantName: req.TenantName,
		TenantCode: tenantCode,
		PlanType:   "trial", // Default to trial for onboarding
	}

	// Call tenant service to create the tenant
	tenantResp, err := s.tenantService.Create(ctx, createTenantReq)
	if err != nil {
		s.logger.Error("Failed to create tenant", "error", err.Error(), "tenant_code", tenantCode)
		return nil, fmt.Errorf("failed to create tenant: %w", err)
	}

	s.logger.Info("Tenant created successfully", "tenant_id", tenantResp.TenantID, "tenant_code", tenantResp.TenantCode)

	// Return onboarding response
	return &dto.CreateTenantOnboardingResponse{
		TenantID:   tenantResp.TenantID,
		TenantName: tenantResp.TenantName,
		TenantCode: tenantResp.TenantCode,
		TenantType: string(req.TenantType),
		Message:    "Tenant created successfully",
	}, nil
}

// CreateWebsite tạo website mới trong quá trình onboarding
func (s *onboardingServiceImpl) CreateWebsite(ctx context.Context, userID, tenantID uint, req *dto.CreateWebsiteOnboardingRequest) (*dto.CreateWebsiteOnboardingResponse, error) {
	s.logger.Info("Creating website during onboarding", "user_id", userID, "tenant_id", tenantID, "website_name", req.Name)

	// Helper function to convert *uint to *int
	uintToIntPtr := func(ptr *uint) *int {
		if ptr != nil {
			val := int(*ptr)
			return &val
		}
		return nil
	}

	// Create website model
	website := &websiteModels.Website{
		TenantID:     int(tenantID),
		Name:         req.Name,
		Subdomain:    req.Subdomain,
		CustomDomain: req.CustomDomain,
		Description:  req.Description,
		Status:       "draft", // Default status for new websites
		ThemeID:      uintToIntPtr(req.ThemeID),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Create website record in database
	if err := s.db.WithContext(ctx).Create(website).Error; err != nil {
		s.logger.Error("Failed to create website", "error", err.Error(), "tenant_id", tenantID)
		return nil, fmt.Errorf("failed to create website: %w", err)
	}

	s.logger.Info("Website created successfully", "website_id", website.WebsiteID, "tenant_id", tenantID)

	return &dto.CreateWebsiteOnboardingResponse{
		WebsiteID:    uint(website.WebsiteID),
		Name:         website.Name,
		Subdomain:    req.Subdomain,
		CustomDomain: req.CustomDomain,
		Message:      "Website created successfully",
	}, nil
}

// GenerateTenantCode tạo tenant code từ tenant name
func (s *onboardingServiceImpl) GenerateTenantCode(ctx context.Context, tenantName string) (string, error) {
	// Normalize the tenant name
	code := strings.ToLower(tenantName)

	// Replace spaces and special characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	code = reg.ReplaceAllString(code, "-")

	// Remove leading/trailing hyphens
	code = strings.Trim(code, "-")

	// Ensure minimum length
	if len(code) < 3 {
		code = code + "-org"
	}

	// Ensure maximum length
	if len(code) > 50 {
		code = code[:50]
	}

	// Check if code is available
	available, err := s.tenantService.CheckTenantCodeAvailability(ctx, code)
	if err != nil {
		return "", fmt.Errorf("failed to check tenant code availability: %w", err)
	}

	// If not available, append a number
	if !available {
		for i := 1; i <= 999; i++ {
			newCode := fmt.Sprintf("%s-%d", code, i)
			if len(newCode) > 50 {
				// If too long, truncate the base code and try again
				baseLen := 50 - len(fmt.Sprintf("-%d", i))
				if baseLen < 3 {
					return "", fmt.Errorf("unable to generate unique tenant code")
				}
				newCode = fmt.Sprintf("%s-%d", code[:baseLen], i)
			}

			available, err := s.tenantService.CheckTenantCodeAvailability(ctx, newCode)
			if err != nil {
				return "", fmt.Errorf("failed to check tenant code availability: %w", err)
			}

			if available {
				code = newCode
				break
			}
		}
	}

	return code, nil
}
