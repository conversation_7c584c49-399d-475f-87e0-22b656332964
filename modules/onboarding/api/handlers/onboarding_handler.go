package handlers

import (
	"fmt"
	"net/http"

	"wnapi/internal/pkg/response"
	"wnapi/modules/onboarding/dto"
	"wnapi/modules/onboarding/service"

	"github.com/gin-gonic/gin"
)

// OnboardingHandler xử lý các request liên quan đến onboarding
type OnboardingHandler struct {
	onboardingService service.OnboardingService
}

// NewOnboardingHandler tạo instance mới của OnboardingHandler
func NewOnboardingHandler(onboardingService service.OnboardingService) *OnboardingHandler {
	return &OnboardingHandler{
		onboardingService: onboardingService,
	}
}

// InitializeOnboarding godoc
// @Summary Khởi tạo tiến trình onboarding
// @Description Tạo các bước onboarding ban đầu cho user mới
// @Tags Onboarding
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.Response{data=dto.OnboardingStatusResponse}
// @Failure 401 {object} response.Response
// @Failure 409 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/onboarding/initialize [post]
func (h *OnboardingHandler) InitializeOnboarding(c *gin.Context) {
	// Lấy user ID từ JWT token
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Lấy tenant ID và website ID từ context hoặc header
	tenantID := c.GetHeader("X-Tenant-ID")
	if tenantID == "" {
		tenantID = "default" // Fallback for now
	}

	websiteID := c.GetHeader("X-Website-ID")
	if websiteID == "" {
		websiteID = "default" // Fallback for now
	}

	// Kiểm tra xem onboarding đã được khởi tạo chưa
	existingStatus, err := h.onboardingService.GetOnboardingStatus(c.Request.Context(), userID.(uint), tenantID, websiteID)
	if err == nil && len(existingStatus.Progress) > 0 {
		response.Error(c, http.StatusConflict, "Onboarding progress has already been initialized for this user.", "ONBOARDING_ALREADY_INITIALIZED")
		return
	}

	status, err := h.onboardingService.InitializeOnboardingProgress(c.Request.Context(), userID.(uint), tenantID, websiteID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to initialize onboarding progress", "INIT_PROGRESS_ERROR")
		return
	}

	response.Success(c, status, nil)
}

// GetOnboardingStatus godoc
// @Summary Lấy trạng thái onboarding của user
// @Description Trả về thông tin chi tiết về tiến trình onboarding của user
// @Tags Onboarding
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.Response{data=dto.OnboardingStatusResponse}
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/onboarding/status [get]
func (h *OnboardingHandler) GetOnboardingStatus(c *gin.Context) {
	// Lấy user ID từ JWT token
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Lấy tenant ID và website ID từ context hoặc header
	tenantID := c.GetHeader("X-Tenant-ID")
	if tenantID == "" {
		tenantID = "default"
	}

	websiteID := c.GetHeader("X-Website-ID")
	if websiteID == "" {
		websiteID = "default"
	}

	status, err := h.onboardingService.GetOnboardingStatus(c.Request.Context(), userID.(uint), tenantID, websiteID)
	if err != nil {
		response.NotFound(c, "Onboarding progress not found. Please initialize first.")
		return
	}

	response.Success(c, status, nil)
}

// CompleteOnboardingStep godoc
// @Summary Hoàn thành một bước onboarding
// @Description Đánh dấu một bước onboarding là đã hoàn thành và xử lý dữ liệu liên quan
// @Tags Onboarding
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.CompleteOnboardingStepRequest true "Complete step request"
// @Success 200 {object} response.Response{data=dto.OnboardingNextStepResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 409 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/onboarding/steps/complete [post]
func (h *OnboardingHandler) CompleteOnboardingStep(c *gin.Context) {
	// Lấy user ID từ JWT token
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	var req dto.CompleteOnboardingStepRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", "VALIDATION_ERROR", nil)
		return
	}

	// Lấy tenant ID và website ID từ context hoặc header
	tenantID := c.GetHeader("X-Tenant-ID")
	if tenantID == "" {
		tenantID = "default"
	}

	websiteID := c.GetHeader("X-Website-ID")
	if websiteID == "" {
		websiteID = "default"
	}

	nextStep, err := h.onboardingService.CompleteOnboardingStep(c.Request.Context(), userID.(uint), tenantID, websiteID, &req)
	if err != nil {
		if err.Error() == "step already completed" {
			response.Error(c, http.StatusConflict, "Step '"+req.StepName+"' has already been completed.", "STEP_ALREADY_COMPLETED")
			return
		}
		response.Error(c, http.StatusInternalServerError, "Failed to process step completion.", "STEP_COMPLETION_FAILED")
		return
	}

	response.Success(c, nextStep, nil)
}

// SkipOnboardingStep godoc
// @Summary Bỏ qua một bước onboarding
// @Description Bỏ qua một bước không bắt buộc trong quá trình onboarding
// @Tags Onboarding
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.SkipOnboardingStepRequest true "Skip step request"
// @Success 200 {object} response.Response{data=dto.OnboardingNextStepResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 409 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/onboarding/steps/skip [post]
func (h *OnboardingHandler) SkipOnboardingStep(c *gin.Context) {
	// Lấy user ID từ JWT token
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	var req dto.SkipOnboardingStepRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", "VALIDATION_ERROR", nil)
		return
	}

	// Lấy tenant ID và website ID từ context hoặc header
	tenantID := c.GetHeader("X-Tenant-ID")
	if tenantID == "" {
		tenantID = "default"
	}

	websiteID := c.GetHeader("X-Website-ID")
	if websiteID == "" {
		websiteID = "default"
	}

	nextStep, err := h.onboardingService.SkipOnboardingStep(c.Request.Context(), userID.(uint), tenantID, websiteID, &req)
	if err != nil {
		if err.Error() == "email verification step cannot be skipped" || err.Error() == "tenant setup step cannot be skipped" {
			response.BadRequest(c, "Step '"+req.StepName+"' is mandatory and cannot be skipped.", "STEP_CANNOT_BE_SKIPPED", nil)
			return
		}
		if err.Error() == "step already completed" {
			response.Error(c, http.StatusConflict, "Step '"+req.StepName+"' has already been completed.", "STEP_ALREADY_COMPLETED")
			return
		}
		response.Error(c, http.StatusInternalServerError, "Failed to skip onboarding step", "SKIP_STEP_ERROR")
		return
	}

	response.Success(c, nextStep, nil)
}

// GetNextOnboardingStep godoc
// @Summary Lấy bước onboarding tiếp theo
// @Description Trả về thông tin về bước onboarding tiếp theo cần thực hiện
// @Tags Onboarding
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.Response{data=dto.OnboardingNextStepResponse}
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/onboarding/next-step [get]
func (h *OnboardingHandler) GetNextOnboardingStep(c *gin.Context) {
	// Lấy user ID từ JWT token
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Lấy tenant ID và website ID từ context hoặc header
	tenantID := c.GetHeader("X-Tenant-ID")
	if tenantID == "" {
		tenantID = "default"
	}

	websiteID := c.GetHeader("X-Website-ID")
	if websiteID == "" {
		websiteID = "default"
	}

	nextStep, err := h.onboardingService.GetNextOnboardingStep(c.Request.Context(), userID.(uint), tenantID, websiteID)
	if err != nil {
		response.NotFound(c, "Onboarding progress not found.")
		return
	}

	response.Success(c, nextStep, nil)
}

// GetTenantNameSuggestions godoc
// @Summary Gợi ý tên tenant
// @Description Gợi ý tên tenant dựa trên tên đầy đủ của người dùng
// @Tags Onboarding
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param full_name query string false "Full name of the user"
// @Success 200 {object} response.Response{data=dto.TenantNameSuggestionsResponse}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/onboarding/suggestions/tenant-name [get]
func (h *OnboardingHandler) GetTenantNameSuggestions(c *gin.Context) {
	// Lấy user ID từ JWT token để xác thực
	_, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	fullName := c.Query("full_name")

	suggestions, err := h.onboardingService.GetTenantNameSuggestions(c.Request.Context(), fullName)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to get tenant name suggestions", "GET_SUGGESTIONS_ERROR")
		return
	}

	response.Success(c, suggestions, nil)
}

// GetWebsiteTemplates godoc
// @Summary Lấy danh sách mẫu website
// @Description Lấy danh sách các mẫu website có sẵn
// @Tags Onboarding
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.Response{data=dto.WebsiteTemplatesResponse}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/onboarding/templates [get]
func (h *OnboardingHandler) GetWebsiteTemplates(c *gin.Context) {
	// Lấy user ID từ JWT token để xác thực
	_, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	templates, err := h.onboardingService.GetWebsiteTemplates(c.Request.Context())
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to get website templates", "GET_TEMPLATES_ERROR")
		return
	}

	response.Success(c, templates, nil)
}

// CreateTenant godoc
// @Summary Tạo tenant mới trong quá trình onboarding
// @Description Tạo tenant mới cho user trong quá trình onboarding
// @Tags Onboarding
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.CreateTenantOnboardingRequest true "Tenant creation request"
// @Success 201 {object} response.Response{data=dto.CreateTenantOnboardingResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 409 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/onboarding/tenant [post]
func (h *OnboardingHandler) CreateTenant(c *gin.Context) {
	// Lấy user ID từ JWT token
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		response.Error(c, http.StatusBadRequest, "Invalid user ID", "INVALID_USER_ID")
		return
	}

	// Parse request body
	var req dto.CreateTenantOnboardingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid request body", "INVALID_REQUEST")
		return
	}

	// Call service to create tenant
	tenant, err := h.onboardingService.CreateTenant(c.Request.Context(), userIDUint, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to create tenant", "CREATE_TENANT_ERROR")
		return
	}

	response.Success(c, tenant, nil)
}

// CreateWebsite godoc
// @Summary Tạo website mới trong quá trình onboarding
// @Description Tạo website mới cho tenant trong quá trình onboarding
// @Tags Onboarding
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.CreateWebsiteOnboardingRequest true "Website creation request"
// @Success 201 {object} response.Response{data=dto.CreateWebsiteOnboardingResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/onboarding/website [post]
func (h *OnboardingHandler) CreateWebsite(c *gin.Context) {
	// Lấy user ID từ JWT token
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		response.Error(c, http.StatusBadRequest, "Invalid user ID", "INVALID_USER_ID")
		return
	}

	// Lấy tenant ID từ header
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	if tenantIDStr == "" {
		response.Error(c, http.StatusBadRequest, "Tenant ID is required", "TENANT_ID_REQUIRED")
		return
	}

	// Convert tenant ID to uint
	var tenantID uint
	if _, err := fmt.Sscanf(tenantIDStr, "%d", &tenantID); err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_TENANT_ID")
		return
	}

	// Parse request body
	var req dto.CreateWebsiteOnboardingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid request body", "INVALID_REQUEST")
		return
	}

	// Call service to create website
	website, err := h.onboardingService.CreateWebsite(c.Request.Context(), userIDUint, tenantID, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to create website", "CREATE_WEBSITE_ERROR")
		return
	}

	response.Success(c, website, nil)
}

// GenerateTenantCode godoc
// @Summary Tạo tenant code từ tenant name
// @Description Tạo tenant code duy nhất từ tenant name
// @Tags Onboarding
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param tenant_name query string true "Tenant name"
// @Success 200 {object} response.Response{data=map[string]string}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/onboarding/generate-tenant-code [get]
func (h *OnboardingHandler) GenerateTenantCode(c *gin.Context) {
	tenantName := c.Query("tenant_name")
	if tenantName == "" {
		response.Error(c, http.StatusBadRequest, "Tenant name is required", "TENANT_NAME_REQUIRED")
		return
	}

	// Call service to generate tenant code
	tenantCode, err := h.onboardingService.GenerateTenantCode(c.Request.Context(), tenantName)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to generate tenant code", "GENERATE_CODE_ERROR")
		return
	}

	response.Success(c, map[string]string{
		"tenant_code": tenantCode,
	}, nil)
}
