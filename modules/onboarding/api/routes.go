package api

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/onboarding/api/handlers"
	"wnapi/modules/onboarding/repository/mysql"
	"wnapi/modules/onboarding/service"
)

// Handler là đối tượng chính xử lý API cho module Onboarding
type Handler struct {
	onboardingHandler *handlers.OnboardingHandler
	jwtService        *auth.JWTService
	logger            logger.Logger
	gormDB            *gorm.DB
}

// NewHandler tạo một handler mới cho onboarding module
func NewHandler(
	onboardingService service.OnboardingService,
	jwtService *auth.JWTService,
	logger logger.Logger,
	gormDB *gorm.DB,
) *Handler {
	return &Handler{
		onboardingHandler: handlers.NewOnboardingHandler(onboardingService),
		jwtService:        jwtService,
		logger:            logger,
		gormDB:            gormDB,
	}
}

// NewHandlerWithDependencies tạo một handler mới với dependencies (fx-compatible signature)
func NewHandlerWithDependencies(
	jwtService *auth.JWTService,
	log logger.Logger,
	gormDB *gorm.DB,
) *Handler {
	// Initialize repository
	repo := mysql.NewOnboardingRepository(gormDB)

	// Initialize service - TODO: Add tenant service dependency
	// For now, we'll pass nil for tenant service and handle it in the service
	onboardingService := service.NewOnboardingService(repo, nil, log, gormDB)

	// Create handler
	onboardingHandler := handlers.NewOnboardingHandler(onboardingService)

	return &Handler{
		onboardingHandler: onboardingHandler,
		jwtService:        jwtService,
		logger:            log,
		gormDB:            gormDB,
	}
}

// RegisterRoutes đăng ký tất cả routes cho module Onboarding
func (h *Handler) RegisterRoutes(router *gin.Engine) error {
	return h.RegisterOnboardingRoutes(router)
}

// RegisterOnboardingRoutes đăng ký routes cho onboarding
func (h *Handler) RegisterOnboardingRoutes(router *gin.Engine) error {
	// Onboarding API Group
	onboardingGroup := router.Group("/api/admin/v1/onboarding")
	onboardingGroup.Use(tracing.GinMiddleware("onboarding"))

	// Health check endpoint
	onboardingGroup.GET("/health", h.healthCheck)

	// Protected endpoints - require JWT authentication
	protectedOnboarding := onboardingGroup.Group("")
	protectedOnboarding.Use(h.jwtService.JWTAuthMiddleware())
	{
		// Core onboarding endpoints
		protectedOnboarding.POST("/initialize", h.onboardingHandler.InitializeOnboarding)
		protectedOnboarding.GET("/status", h.onboardingHandler.GetOnboardingStatus)
		protectedOnboarding.POST("/steps/complete", h.onboardingHandler.CompleteOnboardingStep)
		protectedOnboarding.POST("/steps/skip", h.onboardingHandler.SkipOnboardingStep)
		protectedOnboarding.GET("/next-step", h.onboardingHandler.GetNextOnboardingStep)

		// Helper endpoints
		protectedOnboarding.GET("/suggestions/tenant-name", h.onboardingHandler.GetTenantNameSuggestions)
		protectedOnboarding.GET("/templates", h.onboardingHandler.GetWebsiteTemplates)
	}

	return nil
}

// RegisterRoutesWithServer đăng ký tất cả routes cho module Onboarding với core.Server (for module.go compatibility)
func (h *Handler) RegisterRoutesWithServer(server interface{}) error {
	// Type assertion to handle both *core.Server and *gin.Engine
	switch s := server.(type) {
	case *gin.Engine:
		return h.RegisterRoutes(s)
	default:
		// Assume it's a *core.Server and get the router
		if srv, ok := server.(interface{ GetRouter() *gin.Engine }); ok {
			return h.RegisterRoutes(srv.GetRouter())
		}
		h.logger.Error("Unsupported server type for route registration")
		return nil
	}
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "onboarding",
		"message": "Onboarding module is running",
	})
}

// OnboardingHandler is an alias for Handler to maintain consistency
type OnboardingHandler = Handler

// NewOnboardingHandler creates a new OnboardingHandler (alias for NewHandler)
func NewOnboardingHandler(
	onboardingService service.OnboardingService,
	jwtService *auth.JWTService,
	logger logger.Logger,
	gormDB *gorm.DB,
) *OnboardingHandler {
	return NewHandler(onboardingService, jwtService, logger, gormDB)
}
