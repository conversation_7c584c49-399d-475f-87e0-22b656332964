-- Create user_onboarding_progress table for onboarding module
CREATE TABLE IF NOT EXISTS user_onboarding_progress (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    user_id BIGINT UNSIGNED NOT NULL,
    tenant_id BIGINT UNSIGNED NOT NULL,
    website_id BIGINT UNSIGNED NOT NULL,
    step_name VARCHAR(100) NOT NULL,
    status ENUM('not_started', 'in_progress', 'completed', 'skipped') NOT NULL DEFAULT 'not_started',
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    data JSON NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    INDEX idx_user_tenant_website (user_id, tenant_id, website_id),
    INDEX idx_user_step (user_id, step_name),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_website_id (website_id),
    INDEX idx_status (status),
    UNIQUE KEY unique_user_tenant_website_step (user_id, tenant_id, website_id, step_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
