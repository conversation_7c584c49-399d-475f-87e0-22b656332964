package dto

import (
	"time"
)

// OnboardingStatusRequest DTO cho cập nhật trạng thái onboarding
type OnboardingStatusRequest struct {
	OnboardingStatus string `json:"onboarding_status" binding:"required,oneof=not_started in_progress completed skipped"`
	OnboardingStep   string `json:"onboarding_step,omitempty" binding:"omitempty,oneof=email_verification tenant_setup website_setup profile_setup tutorial"`
}

// OnboardingStatusResponse DTO cho response trạng thái onboarding
type OnboardingStatusResponse struct {
	OnboardingStatus string                     `json:"onboarding_status"`
	OnboardingStep   *string                    `json:"onboarding_step,omitempty"`
	Progress         []OnboardingProgressDetail `json:"progress"`
	CompletedSteps   int                        `json:"completed_steps"`
	TotalSteps       int                        `json:"total_steps"`
	CompletionRate   float64                    `json:"completion_rate"`
}

// OnboardingProgressDetail chi tiết tiến trình từng bước
type OnboardingProgressDetail struct {
	StepName    string     `json:"step_name"`
	Status      string     `json:"status"`
	StartedAt   *time.Time `json:"started_at,omitempty"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	Data        any        `json:"data,omitempty"`
}

// StartOnboardingStepRequest DTO cho bắt đầu bước onboarding
type StartOnboardingStepRequest struct {
	StepName string `json:"step_name" binding:"required,oneof=email_verification tenant_setup website_setup profile_setup tutorial"`
	Data     any    `json:"data,omitempty"`
}

// CompleteOnboardingStepRequest DTO cho hoàn thành bước onboarding
type CompleteOnboardingStepRequest struct {
	StepName string `json:"step_name" binding:"required,oneof=email_verification tenant_setup website_setup profile_setup tutorial"`
	Data     any    `json:"data,omitempty"`
}

// SkipOnboardingStepRequest DTO cho bỏ qua bước onboarding
type SkipOnboardingStepRequest struct {
	StepName string `json:"step_name" binding:"required,oneof=email_verification tenant_setup website_setup profile_setup tutorial"`
	Reason   string `json:"reason,omitempty"`
}

// CreateTenantOnboardingRequest DTO cho tạo tenant trong onboarding
type CreateTenantOnboardingRequest struct {
	TenantType          string  `json:"tenant_type" binding:"required,oneof=individual company"`
	TenantName          string  `json:"tenant_name" binding:"required,min=2,max=255"`
	CompanyName         *string `json:"company_name,omitempty" binding:"omitempty,min=2,max=255"`
	TaxCode             *string `json:"tax_code,omitempty" binding:"omitempty,min=5,max=50"`
	LegalRepresentative *string `json:"legal_representative,omitempty" binding:"omitempty,min=2,max=255"`
	CompanyAddress      *string `json:"company_address,omitempty"`
	CompanyPhone        *string `json:"company_phone,omitempty"`
	CompanyEmail        *string `json:"company_email,omitempty" binding:"omitempty,email"`
}

// CreateTenantOnboardingResponse DTO cho response tạo tenant
type CreateTenantOnboardingResponse struct {
	TenantID   uint   `json:"tenant_id"`
	TenantName string `json:"tenant_name"`
	TenantCode string `json:"tenant_code"`
	TenantType string `json:"tenant_type"`
	Message    string `json:"message"`
}

// CreateWebsiteOnboardingRequest DTO cho tạo website trong onboarding
type CreateWebsiteOnboardingRequest struct {
	Name         string  `json:"name" binding:"required,min=2,max=100"`
	Subdomain    *string `json:"subdomain,omitempty" binding:"omitempty,min=3,max=50,alphanum"`
	CustomDomain *string `json:"custom_domain,omitempty" binding:"omitempty,fqdn"`
	Description  *string `json:"description,omitempty"`
	ThemeID      *uint   `json:"theme_id,omitempty"`
}

// CreateWebsiteOnboardingResponse DTO cho response tạo website
type CreateWebsiteOnboardingResponse struct {
	WebsiteID    uint    `json:"website_id"`
	Name         string  `json:"name"`
	Subdomain    *string `json:"subdomain,omitempty"`
	CustomDomain *string `json:"custom_domain,omitempty"`
	Message      string  `json:"message"`
}

// UpdateProfileOnboardingRequest DTO cho cập nhật profile trong onboarding
type UpdateProfileOnboardingRequest struct {
	FullName    *string `json:"full_name,omitempty" binding:"omitempty,min=2,max=100"`
	PhoneNumber *string `json:"phone_number,omitempty"`
	Address     *string `json:"address,omitempty"`
	Avatar      *string `json:"avatar,omitempty"`
	Bio         *string `json:"bio,omitempty"`
}

// OnboardingNextStepResponse DTO cho response bước tiếp theo
type OnboardingNextStepResponse struct {
	NextStep    *string `json:"next_step,omitempty"`
	IsCompleted bool    `json:"is_completed"`
	Message     string  `json:"message"`
}

// TenantNameSuggestionsResponse DTO cho response gợi ý tên tenant
type TenantNameSuggestionsResponse struct {
	Suggestions []string `json:"suggestions"`
}

// WebsiteTemplatesResponse DTO cho response danh sách mẫu website
type WebsiteTemplatesResponse struct {
	Templates []WebsiteTemplate `json:"templates"`
}

// WebsiteTemplate DTO cho mẫu website
type WebsiteTemplate struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	PreviewURL  string `json:"preview_url"`
	Category    string `json:"category"`
}
