package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// Value chuyển đ<PERSON>i De<PERSON>ult<PERSON>ontent sang định dạng lưu trong DB
func (c DefaultContent) Value() (driver.Value, error) {
	return json.Marshal(c)
}

// <PERSON>an chuyển đổi dữ liệu từ DB sang DefaultContent
func (c *DefaultContent) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(b, &c)
}

// Template mô tả một template trong hệ thống
type Template struct {
	TemplateID     int            `db:"template_id" json:"template_id" gorm:"primaryKey"`
	TenantID       int            `db:"tenant_id" json:"tenant_id"`
	WebsiteID      *int           `db:"website_id" json:"website_id,omitempty"`
	ThemeID        int            `db:"theme_id" json:"theme_id"`
	Name           string         `db:"name" json:"name"`
	Type           string         `db:"type" json:"type"` // page, section, blog, landing, ecommerce, portfolio
	HTMLStructure  string         `db:"html_structure" json:"html_structure"`
	CSS            *string        `db:"css" json:"css,omitempty"`
	DefaultContent DefaultContent `db:"default_content" json:"default_content,omitempty"`
	CreatedAt      time.Time      `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time      `db:"updated_at" json:"updated_at"`

	// Virtual fields (không lưu trong DB)
	Theme *Theme `db:"-" json:"theme,omitempty"`
}

// TableName sets the insert table name for this struct type
func (Template) TableName() string {
	return "website_templates"
}
