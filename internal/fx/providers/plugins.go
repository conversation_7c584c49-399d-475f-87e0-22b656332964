package providers

import (
	"context"
	"fmt"
	"strings"

	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/plugins"

	"go.uber.org/fx"
)

// PluginManager manages plugin lifecycle in FX system
type PluginManager struct {
	registry *plugins.Registry
	config   config.Config
	logger   logger.Logger
	plugins  []plugins.Plugin
}

// NewPluginManager creates a new plugin manager
func NewPluginManager(cfg config.Config, log logger.Logger) *PluginManager {
	return &PluginManager{
		registry: plugins.GlobalRegistry,
		config:   cfg,
		logger:   log,
		plugins:  make([]plugins.Plugin, 0),
	}
}

// LoadPlugins loads enabled plugins from configuration
func (pm *PluginManager) LoadPlugins(ctx context.Context) error {
	// Get enabled plugins from config
	enabledPluginsStr := pm.config.GetStringWithDefault("PLUGINS_ENABLED", "")
	if enabledPluginsStr == "" {
		pm.logger.Info("No plugins configured")
		return nil
	}

	enabledPlugins := strings.Split(enabledPluginsStr, ",")
	for i := range enabledPlugins {
		enabledPlugins[i] = strings.TrimSpace(enabledPlugins[i])
	}

	// Filter empty strings
	var filteredPlugins []string
	for _, plugin := range enabledPlugins {
		if plugin != "" {
			filteredPlugins = append(filteredPlugins, plugin)
		}
	}

	pm.logger.Info("Loading plugins", "count", len(filteredPlugins), "plugins", filteredPlugins)

	// Load each plugin
	for _, pluginName := range filteredPlugins {
		if err := pm.loadPlugin(ctx, pluginName); err != nil {
			pm.logger.Error("Failed to load plugin", "plugin", pluginName, "error", err)
			continue
		}
		pm.logger.Info("Plugin loaded successfully", "plugin", pluginName)
	}

	return nil
}

// loadPlugin loads a single plugin
func (pm *PluginManager) loadPlugin(ctx context.Context, pluginName string) error {
	// Get plugin config from environment variables
	pluginConfig := pm.getPluginConfig(pluginName)

	// Create and initialize plugin
	plugin, err := pm.registry.CreateAndInitialize(ctx, pluginName, pluginConfig)
	if err != nil {
		return fmt.Errorf("failed to create and initialize plugin %s: %w", pluginName, err)
	}

	// Add to managed plugins list
	pm.plugins = append(pm.plugins, plugin)

	return nil
}

// getPluginConfig extracts plugin-specific configuration from environment
func (pm *PluginManager) getPluginConfig(pluginName string) map[string]interface{} {
	config := make(map[string]interface{})

	// Get plugin-specific config using the pattern PLUGIN_{NAME}_{SETTING}
	prefix := "PLUGIN_" + strings.ToUpper(strings.ReplaceAll(pluginName, "-", "_"))
	
	// Common plugin settings
	if enabled := pm.config.GetString(prefix + "_ENABLED"); enabled != "" {
		config["enabled"] = pm.config.GetBool(prefix + "_ENABLED")
	} else {
		config["enabled"] = true // Default to enabled if not specified
	}

	if timeout := pm.config.GetString(prefix + "_TIMEOUT"); timeout != "" {
		config["timeout"] = pm.config.GetInt(prefix + "_TIMEOUT")
	}

	if interactive := pm.config.GetString(prefix + "_INTERACTIVE"); interactive != "" {
		config["interactive"] = pm.config.GetBool(prefix + "_INTERACTIVE")
	}

	if maxRetries := pm.config.GetString(prefix + "_MAX_RETRIES"); maxRetries != "" {
		config["max_retries"] = pm.config.GetInt(prefix + "_MAX_RETRIES")
	}

	if retryDelay := pm.config.GetString(prefix + "_RETRY_DELAY"); retryDelay != "" {
		config["retry_delay"] = pm.config.GetInt(prefix + "_RETRY_DELAY")
	}

	// Plugin-specific settings for logger plugin
	if pluginName == "logger" {
		if level := pm.config.GetString(prefix + "_LEVEL"); level != "" {
			config["level"] = level
		}
		if file := pm.config.GetString(prefix + "_FILE"); file != "" {
			config["file"] = file
		}
		if rotationSize := pm.config.GetString(prefix + "_ROTATION_SIZE"); rotationSize != "" {
			config["rotation_size"] = pm.config.GetInt(prefix + "_ROTATION_SIZE")
		}
		if rotationCount := pm.config.GetString(prefix + "_ROTATION_COUNT"); rotationCount != "" {
			config["rotation_count"] = pm.config.GetInt(prefix + "_ROTATION_COUNT")
		}
	}

	return config
}

// Shutdown shuts down all managed plugins
func (pm *PluginManager) Shutdown(ctx context.Context) error {
	pm.logger.Info("Shutting down plugins", "count", len(pm.plugins))

	for _, plugin := range pm.plugins {
		if err := plugin.Shutdown(ctx); err != nil {
			pm.logger.Error("Error shutting down plugin", "plugin", plugin.Name(), "error", err)
		} else {
			pm.logger.Info("Plugin shut down successfully", "plugin", plugin.Name())
		}
	}

	return nil
}

// GetPlugins returns all loaded plugins
func (pm *PluginManager) GetPlugins() []plugins.Plugin {
	return pm.plugins
}

// GetPlugin returns a specific plugin by name
func (pm *PluginManager) GetPlugin(name string) (plugins.Plugin, bool) {
	for _, plugin := range pm.plugins {
		if plugin.Name() == name {
			return plugin, true
		}
	}
	return nil, false
}

// NewPluginProvider provides plugin manager for FX
func NewPluginProvider(cfg config.Config, log logger.Logger) *PluginManager {
	return NewPluginManager(cfg, log)
}

// RegisterPluginHooks registers plugin lifecycle hooks with FX
func RegisterPluginHooks(
	lc fx.Lifecycle,
	pm *PluginManager,
	log logger.Logger,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			log.Info("Starting plugin system")
			return pm.LoadPlugins(ctx)
		},
		OnStop: func(ctx context.Context) error {
			log.Info("Stopping plugin system")
			return pm.Shutdown(ctx)
		},
	})
}
