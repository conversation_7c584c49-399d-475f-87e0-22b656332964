// Package core provides backward compatibility stubs for legacy code
// All functionality has been migrated to internal/fx/ but these stubs
// are kept to avoid breaking existing modules during the migration period
package core

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
	"wnapi/internal/pkg/cache"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/plugins"
)

// AppBootstrap stub for backward compatibility
// DEPRECATED: Use internal/fx/app.go instead
type AppBootstrap struct {
	Config            config.Config
	Logger            logger.Logger
	DBManager         *database.DBManager
	Cache             cache.Cache
	MiddlewareFactory *permission.MiddlewareFactory
	ServiceRegistry   *ServiceRegistry
}

// NewAppBootstrap creates a new AppBootstrap instance
// DEPRECATED: Use internal/fx/app.go instead
func NewAppBootstrap(
	cfg config.Config,
	log logger.Logger,
	dbm *database.DBManager,
	appCache cache.Cache,
	mwFactory *permission.MiddlewareFactory,
) *AppBootstrap {
	return &AppBootstrap{
		Config:            cfg,
		Logger:            log,
		DBManager:         dbm,
		Cache:             appCache,
		MiddlewareFactory: mwFactory,
		ServiceRegistry:   NewServiceRegistry(),
	}
}

// Getter methods for AppBootstrap
func (a *AppBootstrap) GetConfig() config.Config                              { return a.Config }
func (a *AppBootstrap) GetLogger() logger.Logger                              { return a.Logger }
func (a *AppBootstrap) GetDBManager() *database.DBManager                     { return a.DBManager }
func (a *AppBootstrap) GetCache() cache.Cache                                 { return a.Cache }
func (a *AppBootstrap) GetMiddlewareFactory() *permission.MiddlewareFactory   { return a.MiddlewareFactory }
func (a *AppBootstrap) GetServiceRegistry() *ServiceRegistry                  { return a.ServiceRegistry }
func (a *AppBootstrap) GetModules() []Module                                  { return nil }
func (a *AppBootstrap) GetPlugins() []plugins.Plugin                          { return nil }
func (a *AppBootstrap) Initialize() error                                     { return nil }
func (a *AppBootstrap) Shutdown(ctx context.Context) error                    { return nil }

// Module interface stub for backward compatibility
// DEPRECATED: Use internal/fx/modules/ instead
type Module interface {
	Name() string
	Init(ctx context.Context) error
	RegisterRoutes(router *Server) error
	Cleanup(ctx context.Context) error
	GetMigrationPath() string
	GetMigrationOrder() int
}

// ModuleFactory stub for backward compatibility
// DEPRECATED: Use internal/fx/modules/ instead
type ModuleFactory func(app *AppBootstrap, config map[string]interface{}) (Module, error)

// Server interface stub for backward compatibility
// DEPRECATED: Use internal/fx/lifecycle/server.go instead
type Server struct {
	app    *AppBootstrap
	engine *gin.Engine
	logger logger.Logger
	server *http.Server
}

// NewServer creates a new Server instance
// DEPRECATED: Use internal/fx/lifecycle/server.go instead
func NewServer(app *AppBootstrap) *Server {
	engine := gin.Default()
	engine.RedirectTrailingSlash = false

	return &Server{
		app:    app,
		engine: engine,
		logger: app.Logger,
		server: &http.Server{},
	}
}

// Server methods for backward compatibility
func (s *Server) Start() error                               { return nil }
func (s *Server) Shutdown(ctx context.Context) error         { return nil }
func (s *Server) Group(relativePath string) *gin.RouterGroup { return s.engine.Group(relativePath) }
func (s *Server) GetRouter() *gin.Engine                     { return s.engine }
func (s *Server) GetAppBootstrap() *AppBootstrap             { return s.app }

// ModuleRegistry stub for backward compatibility
// DEPRECATED: Use internal/fx/modules/ instead
type ModuleRegistry struct {
	factories map[string]ModuleFactory
	modules   map[string]Module
}

// GlobalModuleRegistry stub for backward compatibility
// DEPRECATED: Use internal/fx/modules/ instead
var GlobalModuleRegistry = NewModuleRegistry()

// NewModuleRegistry creates a new ModuleRegistry
// DEPRECATED: Use internal/fx/modules/ instead
func NewModuleRegistry() *ModuleRegistry {
	return &ModuleRegistry{
		factories: make(map[string]ModuleFactory),
		modules:   make(map[string]Module),
	}
}

// RegisterModuleFactory registers a module factory
// DEPRECATED: Use internal/fx/modules/ instead
func RegisterModuleFactory(name string, factory ModuleFactory) {
	if name == "" || factory == nil {
		return
	}
	GlobalModuleRegistry.Register(name, factory)
}

// ModuleRegistry methods for backward compatibility
func (r *ModuleRegistry) Register(name string, factory ModuleFactory) {
	if r == nil || factory == nil {
		return
	}
	r.factories[name] = factory
}

func (r *ModuleRegistry) Get(name string) (ModuleFactory, bool) {
	if r == nil || name == "" {
		return nil, false
	}
	factory, ok := r.factories[name]
	return factory, ok
}

func (r *ModuleRegistry) Factories() map[string]ModuleFactory {
	if r == nil {
		return make(map[string]ModuleFactory)
	}
	result := make(map[string]ModuleFactory, len(r.factories))
	for k, v := range r.factories {
		result[k] = v
	}
	return result
}
